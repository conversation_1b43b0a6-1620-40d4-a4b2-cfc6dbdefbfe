import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Container,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Email as EmailIcon,
  Refresh as RefreshIcon,
  Login as LoginIcon,
} from '@mui/icons-material';
import { useLocation, Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import { resendVerificationEmail } from '../../api/auth';

const MotionBox = motion(Box);
const MotionPaper = motion(Paper);

interface LocationState {
  email?: string;
  message?: string;
}

const EmailVerificationPending: React.FC = () => {
  const location = useLocation();
  const theme = useTheme();
  
  const [resendLoading, setResendLoading] = useState(false);
  const [resendMessage, setResendMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const state = location.state as LocationState;
  const email = state?.email;
  const message = state?.message;

  const handleResendVerification = async () => {
    if (!email) {
      setError('Email address is required to resend verification.');
      return;
    }
    
    setResendLoading(true);
    setResendMessage(null);
    setError(null);
    
    try {
      const response = await resendVerificationEmail(email);
      setResendMessage(response.message);
    } catch (err: any) {
      console.error('Resend verification error:', err);
      setError(err.response?.data?.detail || 'Failed to resend verification email.');
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
      }}
    >
      <Container maxWidth="sm">
        <MotionPaper
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          elevation={8}
          sx={{
            p: 4,
            borderRadius: 3,
            textAlign: 'center',
          }}
        >
          <Stack spacing={3} alignItems="center">
            {/* Icon */}
            <MotionBox
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            >
              <EmailIcon sx={{ fontSize: 60, color: 'primary.main' }} />
            </MotionBox>

            {/* Title */}
            <Typography variant="h4" component="h1" fontWeight="bold">
              Check Your Email
            </Typography>

            {/* Content */}
            <Stack spacing={2}>
              <Typography variant="body1" color="text.secondary">
                {message || 'We\'ve sent a verification link to your email address.'}
              </Typography>
              
              {email && (
                <Typography variant="body1" fontWeight={500}>
                  {email}
                </Typography>
              )}
              
              <Typography variant="body2" color="text.secondary">
                Click the link in the email to verify your account. If you don't see the email, check your spam folder.
              </Typography>
            </Stack>

            {/* Messages */}
            {resendMessage && (
              <Alert severity="success" sx={{ width: '100%' }}>
                {resendMessage}
              </Alert>
            )}

            {error && (
              <Alert severity="error" sx={{ width: '100%' }}>
                {error}
              </Alert>
            )}

            {/* Actions */}
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mt: 3 }}>
              {email && (
                <Button
                  variant="contained"
                  startIcon={resendLoading ? <CircularProgress size={20} /> : <RefreshIcon />}
                  onClick={handleResendVerification}
                  disabled={resendLoading}
                >
                  {resendLoading ? 'Sending...' : 'Resend Email'}
                </Button>
              )}
              
              <Button
                variant="outlined"
                component={RouterLink}
                to="/login"
                startIcon={<LoginIcon />}
              >
                Back to Login
              </Button>
            </Stack>

            {/* Instructions */}
            <Box
              sx={{
                mt: 4,
                p: 3,
                bgcolor: alpha(theme.palette.info.main, 0.1),
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
              }}
            >
              <Typography variant="subtitle2" color="info.main" gutterBottom>
                📧 Email Verification Steps:
              </Typography>
              <Stack spacing={1} sx={{ textAlign: 'left' }}>
                <Typography variant="body2" color="text.secondary">
                  1. Check your email inbox for a message from CampusPQ
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  2. Click the "Verify Email Address" button in the email
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  3. You'll be redirected back to login once verified
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  4. If you don't see the email, check your spam/junk folder
                </Typography>
              </Stack>
            </Box>

            {/* Help text */}
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Still having trouble? Contact us at{' '}
              <Typography component="span" color="primary.main" fontWeight={500}>
                <EMAIL>
              </Typography>
            </Typography>
          </Stack>
        </MotionPaper>
      </Container>
    </Box>
  );
};

export default EmailVerificationPending;
