import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Link,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  InputAdornment,
  IconButton,
  Fade,
  Grid,
  useTheme
} from '@mui/material';
import {
  Person as PersonIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';
import AnimatedBackground from '../../components/AnimatedBackground';
import EducationalIllustration from '../../components/EducationalIllustration';

interface LocationState {
  message?: string;
}

const MotionBox = motion(Box);

const LoginNew: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Check for success message in location state (e.g., from registration)
  useEffect(() => {
    const state = location.state as LocationState;
    if (state?.message) {
      setSuccessMessage(state.message);
      // Clear the location state to prevent showing the message again on refresh
      navigate(location.pathname, { replace: true });
    }
  }, [location, navigate]);

  const formik = useFormik({
    initialValues: {
      username: '',
      password: '',
    },
    validationSchema: Yup.object({
      username: Yup.string().required('Username or email is required'),
      password: Yup.string().required('Password is required'),
    }),
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);
      try {
        console.log('Starting login process...');
        await login(values);
        console.log('Login successful, navigating to dashboard');
        navigate('/dashboard');
      } catch (err: any) {
        console.error('Login error in component:', err);

        // Handle network errors
        if (!err.response) {
          setError('Network error: Unable to connect to the server. Please check your internet connection and try again.');
        } else {
          const errorDetail = err.response?.data?.detail || 'Failed to login. Please check your credentials.';
          const status = err.response?.status;

          // Handle specific error cases
          if (status === 404) {
            setError('No account found with this email address. Please check your email or register for a new account.');
          } else if (status === 401) {
            setError('Incorrect password. Please check your password and try again.');
          } else if (status === 403 && (errorDetail.includes('Email not verified') || err.response?.headers?.['x-email-verification-required'])) {
            // Get email from response headers or use the input email
            const userEmail = err.response?.headers?.['x-user-email'] || values.username;

            // Redirect to email verification pending page
            navigate('/verify-email-pending', {
              state: {
                email: userEmail,
                message: 'A verification email has been sent to your email address. Please check your email and verify your account before logging in.'
              }
            });
            return;
          } else if (status === 400 && errorDetail.includes('deactivated')) {
            setError('Your account has been deactivated. Please contact support for assistance.');
          } else {
            setError(errorDetail);
          }
        }
      } finally {
        setLoading(false);
      }
    },
  });

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <AnimatedBackground>
      <Grid
        container
        sx={{
          maxWidth: { xs: '100%', md: 1000 },
          minHeight: { xs: 'auto', md: 600 },
          borderRadius: 3,
          overflow: 'hidden',
          backgroundColor: theme.palette.background.paper,
        }}
      >
        {/* Left side - Login Form */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              borderRadius: 0,
              boxShadow: 'none',
              border: 'none',
            }}
          >
            <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
              <MotionBox
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                sx={{ textAlign: 'center', mb: 4 }}
              >
                <SchoolIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
                <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                  Welcome Back
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Sign in to continue to CampusPQ
                </Typography>
              </MotionBox>

              {successMessage && (
                <Fade in={true} timeout={1000}>
                  <Alert
                    severity="success"
                    sx={{
                      mb: 3,
                      borderRadius: 2,
                      maxWidth: '100%',
                      wordBreak: 'break-word'
                    }}
                  >
                    {successMessage}
                  </Alert>
                </Fade>
              )}

              {error && (
                <Fade in={true} timeout={1000}>
                  <Alert
                    severity="error"
                    sx={{
                      mb: 3,
                      borderRadius: 2,
                      maxWidth: '100%',
                      wordBreak: 'break-word'
                    }}
                  >
                    {error}
                  </Alert>
                </Fade>
              )}

            <form onSubmit={formik.handleSubmit}>
              <MotionBox
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <TextField
                  fullWidth
                  id="username"
                  name="username"
                  label="Username or Email"
                  variant="outlined"
                  margin="normal"
                  value={formik.values.username}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.username && Boolean(formik.errors.username)}
                  helperText={formik.touched.username && formik.errors.username}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="primary" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                      },
                      '&.Mui-focused': {
                        transform: 'translateY(-2px)',
                      }
                    }
                  }}
                />
              </MotionBox>

              <MotionBox
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  variant="outlined"
                  margin="normal"
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.password && Boolean(formik.errors.password)}
                  helperText={formik.touched.password && formik.errors.password}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon color="primary" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                      },
                      '&.Mui-focused': {
                        transform: 'translateY(-2px)',
                      }
                    }
                  }}
                />
              </MotionBox>

              <MotionBox
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Button
                  fullWidth
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  disabled={loading}
                  sx={{
                    mt: 2,
                    mb: 3,
                    py: 1.5,
                    borderRadius: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                    }
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </MotionBox>

              <MotionBox
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                sx={{ textAlign: 'center' }}
              >
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <Link
                    component={RouterLink}
                    to="/forgot-password"
                    sx={{
                      fontWeight: 'bold',
                      color: 'primary.main',
                      textDecoration: 'none',
                      transition: 'all 0.2s',
                      '&:hover': {
                        color: 'secondary.main',
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    Forgot your password?
                  </Link>
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Don't have an account?{' '}
                  <Link
                    component={RouterLink}
                    to="/register"
                    sx={{
                      fontWeight: 'bold',
                      color: 'primary.main',
                      textDecoration: 'none',
                      transition: 'all 0.2s',
                      '&:hover': {
                        color: 'secondary.main',
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    Create an account
                  </Link>
                </Typography>


              </MotionBox>
            </form>
          </CardContent>
          </Card>
        </Grid>

        {/* Right side - Educational Illustration */}
        <Grid
          item
          md={6}
          sx={{
            display: { xs: 'none', md: 'block' },
            bgcolor: theme.palette.mode === 'light'
              ? 'rgba(0, 0, 0, 0.02)'
              : 'rgba(255, 255, 255, 0.02)',
          }}
        >
          <EducationalIllustration
            title="Welcome to CampusPQ"
            subtitle="Your AI-powered learning companion for academic excellence"
          />
        </Grid>
      </Grid>
    </AnimatedBackground>
  );
};

export default LoginNew;
