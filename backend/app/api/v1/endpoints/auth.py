from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.core import security
from app.core.config import settings
from app.services import user_service

router = APIRouter()


@router.post("/login", response_model=schemas.Token)
def login_access_token(
    db: Session = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # First check if user exists
    user_exists = user_service.get_by_email(db, email=form_data.username)

    if not user_exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No account found with this email address. Please check your email or register for a new account.",
        )

    # Then authenticate
    user = user_service.authenticate(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect password. Please check your password and try again.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    elif not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Your account has been deactivated. Please contact support for assistance."
        )
    elif not user.email_verified:
        # Automatically resend verification email
        from app.services.email_service import send_verification_email
        from app.services.user_service import generate_verification_token
        from datetime import datetime, timedelta

        # Generate new verification token if needed
        if not user.email_verification_token or user.email_verification_token_expires < datetime.utcnow():
            verification_token = generate_verification_token()
            expires_at = datetime.utcnow() + timedelta(hours=settings.EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS)

            user.email_verification_token = verification_token
            user.email_verification_token_expires = expires_at
            db.add(user)
            db.commit()

            # Send verification email
            send_verification_email(
                email_to=user.email,
                verification_token=verification_token,
                user_name=user.full_name
            )

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email not verified. A new verification email has been sent to your email address. Please check your email and verify your account before logging in.",
            headers={
                "X-Email-Verification-Required": "true",
                "X-User-Email": user.email
            },
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            user.id, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
    }


@router.post("/register", response_model=schemas.User)
def register_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: schemas.UserCreate,
) -> Any:
    """
    Register a new user and send verification email
    """
    user = user_service.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A user with this email already exists",
        )
    user = user_service.create_with_verification(db, obj_in=user_in)
    return user


@router.post("/complete-profile", response_model=schemas.User)
def complete_profile(
    *,
    db: Session = Depends(deps.get_db),
    profile_data: schemas.ProfileComplete,
    current_user: schemas.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Complete user profile with additional information
    """
    # Check if email is verified before allowing profile completion
    if not current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email must be verified before completing profile. Please check your email and verify your account.",
            headers={"X-Email-Verification-Required": "true"},
        )

    if current_user.profile_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Profile already completed",
        )

    # Update user with profile data
    update_data = profile_data.model_dump()
    update_data["profile_completed"] = True

    user = user_service.update(db, db_obj=current_user, obj_in=update_data)
    return user


@router.post("/verify-email", response_model=schemas.EmailVerificationResponse)
def verify_email(
    *,
    db: Session = Depends(deps.get_db),
    verification_data: schemas.EmailVerificationConfirm,
) -> Any:
    """
    Verify email address using verification token
    """
    user = user_service.verify_email(db, token=verification_data.token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token",
        )

    return {
        "message": "Email verified successfully! You can now log in to your account.",
        "success": True
    }


@router.post("/resend-verification", response_model=schemas.EmailVerificationResponse)
def resend_verification_email(
    *,
    db: Session = Depends(deps.get_db),
    email_data: schemas.EmailVerificationRequest,
) -> Any:
    """
    Resend email verification email
    """
    user = user_service.get_by_email(db, email=email_data.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User with this email not found",
        )

    if user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is already verified",
        )

    success = user_service.resend_verification_email(db, email=email_data.email)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email",
        )

    return {
        "message": "Verification email sent successfully! Please check your email.",
        "success": True
    }


@router.post("/test-email", response_model=schemas.EmailVerificationResponse)
def test_email_sending(
    *,
    db: Session = Depends(deps.get_db),
    email_data: schemas.EmailVerificationRequest,
) -> Any:
    """
    Test email sending functionality (development only)
    """
    from app.services.email_service import send_verification_email, send_email
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Testing email sending to: {email_data.email}")

    # Generate a test token
    test_token = "test_token_12345"

    # Send test verification email
    success = send_verification_email(
        email_to=email_data.email,
        verification_token=test_token,
        user_name="Test User"
    )

    if not success:
        logger.error(f"Failed to send test email to {email_data.email}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send test email. Check server logs for details.",
        )

    logger.info(f"Test email sent successfully to {email_data.email}")
    return {
        "message": f"Test email sent successfully to {email_data.email}! Check your inbox or the development_emails folder.",
        "success": True
    }


@router.post("/test-smtp", response_model=schemas.EmailVerificationResponse)
def test_smtp_connection(
    *,
    email_data: schemas.EmailVerificationRequest,
) -> Any:
    """
    Test SMTP connection and basic email sending (development only)
    """
    from app.services.email_service import send_email
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Testing SMTP connection and sending simple email to: {email_data.email}")

    # Simple test email
    subject = "CampusPQ SMTP Test"
    html_content = """
    <html>
    <body>
        <h2>SMTP Test Email</h2>
        <p>This is a test email to verify SMTP configuration is working correctly.</p>
        <p>If you received this email, the SMTP setup is functioning properly.</p>
        <p>Best regards,<br>CampusPQ Development Team</p>
    </body>
    </html>
    """
    text_content = """
    SMTP Test Email

    This is a test email to verify SMTP configuration is working correctly.
    If you received this email, the SMTP setup is functioning properly.

    Best regards,
    CampusPQ Development Team
    """

    success = send_email(
        email_to=email_data.email,
        subject=subject,
        html_content=html_content,
        text_content=text_content
    )

    if not success:
        logger.error(f"Failed to send SMTP test email to {email_data.email}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send SMTP test email. Check server logs for details.",
        )

    logger.info(f"SMTP test email sent successfully to {email_data.email}")
    return {
        "message": f"SMTP test email sent successfully to {email_data.email}! Check your inbox or the development_emails folder.",
        "success": True
    }


@router.post("/forgot-password", response_model=schemas.PasswordResetResponse)
def forgot_password(
    *,
    db: Session = Depends(deps.get_db),
    password_reset_data: schemas.PasswordResetRequest,
) -> Any:
    """
    Request password reset email
    """
    success = user_service.request_password_reset(db, email=password_reset_data.email)

    # Always return success for security (don't reveal if email exists)
    return {
        "message": "If an account with this email exists, you will receive a password reset link shortly.",
        "success": True
    }


@router.post("/reset-password", response_model=schemas.PasswordResetResponse)
def reset_password(
    *,
    db: Session = Depends(deps.get_db),
    password_reset_data: schemas.PasswordResetConfirm,
) -> Any:
    """
    Reset password using reset token
    """
    user = user_service.reset_password(
        db,
        token=password_reset_data.token,
        new_password=password_reset_data.new_password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired password reset token",
        )

    return {
        "message": "Password reset successfully! You can now log in with your new password.",
        "success": True
    }
