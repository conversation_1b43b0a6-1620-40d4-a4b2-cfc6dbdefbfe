import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from pathlib import Path

from app.core.config import settings

logger = logging.getLogger(__name__)


def _save_email_to_file(email_to: str, subject: str, html_content: str, text_content: Optional[str] = None) -> bool:
    """
    Save email to file for development testing
    """
    try:
        import os
        from datetime import datetime

        # Create emails directory if it doesn't exist
        emails_dir = "development_emails"
        os.makedirs(emails_dir, exist_ok=True)

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_email = email_to.replace("@", "_at_").replace(".", "_")
        filename = f"{timestamp}_{safe_email}.html"
        filepath = os.path.join(emails_dir, filename)

        # Create complete HTML email
        complete_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{subject}</title>
    <style>
        .email-header {{
            background-color: #f0f0f0;
            padding: 20px;
            border-bottom: 2px solid #1976d2;
            margin-bottom: 20px;
        }}
        .email-info {{
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #1976d2;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="email-header">
        <h1>📧 Development Email Preview</h1>
        <p><strong>This email would be sent in production</strong></p>
    </div>

    <div class="email-info">
        <p><strong>To:</strong> {email_to}</p>
        <p><strong>Subject:</strong> {subject}</p>
        <p><strong>Timestamp:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>

    <div class="email-content">
        {html_content}
    </div>

    {f'<div class="text-content"><h3>Text Version:</h3><pre>{text_content}</pre></div>' if text_content else ''}
</body>
</html>
        """

        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(complete_html)

        logger.info(f"📧 Development email saved to: {filepath}")
        logger.info(f"📧 Email details - To: {email_to}, Subject: {subject}")

        # Also log the verification link for easy access
        if "verify-email" in html_content:
            import re
            link_match = re.search(r'href="([^"]*verify-email[^"]*)"', html_content)
            if link_match:
                verification_link = link_match.group(1)
                logger.info(f"🔗 VERIFICATION LINK: {verification_link}")
                print(f"\n{'='*80}")
                print(f"📧 EMAIL VERIFICATION LINK FOR {email_to}:")
                print(f"🔗 {verification_link}")
                print(f"📁 Full email saved to: {filepath}")
                print(f"{'='*80}\n")

        return True

    except Exception as e:
        logger.error(f"Failed to save development email: {str(e)}")
        return False


def send_email(
    email_to: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
) -> bool:
    """
    Send an email using SMTP with enhanced error handling and fallback
    """
    try:
        # Check if SMTP is configured
        if not settings.SMTP_USER or not settings.SMTP_PASSWORD:
            logger.error("SMTP credentials not configured. Please set SMTP_USER and SMTP_PASSWORD in .env file")
            logger.info("Falling back to development email file saving...")
            return _save_email_to_file(email_to, subject, html_content, text_content)

        logger.info(f"Attempting to send email to {email_to} via SMTP...")
        logger.debug(f"SMTP Config - Host: {settings.SMTP_HOST}, Port: {settings.SMTP_PORT}, User: {settings.SMTP_USER}")

        # Create message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = f"{settings.EMAILS_FROM_NAME} <{settings.EMAILS_FROM_EMAIL or settings.SMTP_USER}>"
        msg["To"] = email_to

        # Add text content
        if text_content:
            text_part = MIMEText(text_content, "plain")
            msg.attach(text_part)

        # Add HTML content
        html_part = MIMEText(html_content, "html")
        msg.attach(html_part)

        # Send email with detailed error handling
        with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
            # Enable debug output for troubleshooting
            if settings.DEBUG:
                server.set_debuglevel(1)

            logger.debug("Starting TLS...")
            server.starttls()

            logger.debug("Logging in to SMTP server...")
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)

            logger.debug("Sending message...")
            server.send_message(msg)

        logger.info(f"✅ Email sent successfully to {email_to}")
        return True

    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"❌ SMTP Authentication failed for {email_to}: {str(e)}")
        logger.error("Check your Gmail app password. Regular Gmail password won't work.")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)

    except smtplib.SMTPRecipientsRefused as e:
        logger.error(f"❌ SMTP Recipients refused for {email_to}: {str(e)}")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)

    except smtplib.SMTPServerDisconnected as e:
        logger.error(f"❌ SMTP Server disconnected for {email_to}: {str(e)}")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)

    except Exception as e:
        logger.error(f"❌ Failed to send email to {email_to}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)


def send_verification_email(email_to: str, verification_token: str, user_name: str) -> bool:
    """
    Send email verification email
    """
    verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
    
    subject = "Verify your CampusPQ account"
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 10px;
            }}
            .title {{
                font-size: 24px;
                color: #333;
                margin-bottom: 20px;
            }}
            .content {{
                font-size: 16px;
                margin-bottom: 30px;
            }}
            .button {{
                display: inline-block;
                background-color: #1976d2;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
            }}
            .button:hover {{
                background-color: #1565c0;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 14px;
                color: #666;
                text-align: center;
            }}
            .warning {{
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🎓 CampusPQ</div>
                <h1 class="title">Verify Your Email Address</h1>
            </div>
            
            <div class="content">
                <p>Hello {user_name},</p>
                
                <p>Thank you for registering with CampusPQ! To complete your account setup and start your learning journey, please verify your email address by clicking the button below:</p>
                
                <div style="text-align: center;">
                    <a href="{verification_url}" class="button">Verify Email Address</a>
                </div>
                
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                    {verification_url}
                </p>
                
                <div class="warning">
                    <strong>Important:</strong> This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to request a new verification email.
                </div>
                
                <p>Once verified, you'll be able to:</p>
                <ul>
                    <li>Access your personalized dashboard</li>
                    <li>Take practice exams and quizzes</li>
                    <li>Track your learning progress</li>
                    <li>Connect with tutors and peers</li>
                </ul>
                
                <p>If you didn't create an account with CampusPQ, please ignore this email.</p>
            </div>
            
            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """
    
    text_content = f"""
    Hello {user_name},

    Thank you for registering with CampusPQ! To complete your account setup, please verify your email address by visiting this link:

    {verification_url}

    This verification link will expire in 24 hours.

    If you didn't create an account with CampusPQ, please ignore this email.

    Best regards,
    The CampusPQ Team
    """
    
    return send_email(email_to, subject, html_content, text_content)


def send_password_reset_email(email_to: str, reset_token: str, user_name: str) -> bool:
    """
    Send password reset email
    """
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"

    subject = "Reset your CampusPQ password"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }}
            .container {{
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e0e0e0;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 10px;
            }}
            .content {{
                margin-bottom: 30px;
            }}
            .reset-button {{
                display: inline-block;
                background-color: #dc2626;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }}
            .reset-button:hover {{
                background-color: #b91c1c;
            }}
            .warning {{
                background-color: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
                color: #666;
                font-size: 14px;
            }}
            .security-note {{
                background-color: #f3f4f6;
                border-left: 4px solid #6b7280;
                padding: 15px;
                margin: 20px 0;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CampusPQ</div>
                <h1>Password Reset Request</h1>
            </div>

            <div class="content">
                <p>Hello {user_name},</p>

                <p>We received a request to reset your password for your CampusPQ account. If you made this request, click the button below to reset your password:</p>

                <div style="text-align: center;">
                    <a href="{reset_url}" class="reset-button">Reset My Password</a>
                </div>

                <div class="warning">
                    <strong>⚠️ Important:</strong> This password reset link will expire in 1 hour for security reasons.
                </div>

                <div class="security-note">
                    <strong>Security Notice:</strong>
                    <ul>
                        <li>If you didn't request this password reset, please ignore this email</li>
                        <li>Your password will remain unchanged if you don't click the reset link</li>
                        <li>Never share this reset link with anyone</li>
                        <li>If you're concerned about your account security, contact our support team</li>
                    </ul>
                </div>

                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 5px; font-family: monospace;">
                    {reset_url}
                </p>
            </div>

            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Hello {user_name},

    We received a request to reset your password for your CampusPQ account.

    To reset your password, please visit this link:
    {reset_url}

    This password reset link will expire in 1 hour for security reasons.

    If you didn't request this password reset, please ignore this email. Your password will remain unchanged.

    For security reasons, never share this reset link with anyone.

    Best regards,
    The CampusPQ Team
    """

    return send_email(email_to, subject, html_content, text_content)
